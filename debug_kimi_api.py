#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Kimi API连接问题的脚本
"""

import sys
import os
import requests
import json
from openai import OpenAI

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_kimi_api_direct():
    """直接测试Kimi API"""
    print("=== 直接测试Kimi API ===")
    
    api_key = "sk-nGhmtCDstrz6ekvzpPo0S7jD9sLb2YIsr6LAhkKIa7gUG9tL"
    base_url = "https://api.moonshot.cn/anthropic"
    
    # 测试1: 检查API端点是否可访问
    print("1. 测试API端点可访问性...")
    try:
        response = requests.get(base_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
    except Exception as e:
        print(f"   ❌ 无法访问API端点: {e}")
        return
    
    # 测试2: 使用requests直接调用
    print("\n2. 使用requests直接调用...")
    try:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        data = {
            "model": "kimi-k2-0711-preview",
            "max_tokens": 100,
            "messages": [
                {"role": "user", "content": "请回复'测试成功'"}
            ]
        }
        
        response = requests.post(
            f"{base_url}/v1/messages",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
    except Exception as e:
        print(f"   ❌ requests调用失败: {e}")
    
    # 测试3: 尝试不同的端点
    print("\n3. 尝试不同的API端点...")
    endpoints = [
        "https://api.moonshot.cn/v1/chat/completions",
        "https://api.moonshot.cn/anthropic/v1/messages",
        "https://api.moonshot.ai/v1/chat/completions",
        "https://api.moonshot.ai/anthropic/v1/messages"
    ]
    
    for endpoint in endpoints:
        print(f"   测试端点: {endpoint}")
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            # OpenAI格式
            if "chat/completions" in endpoint:
                data = {
                    "model": "kimi-k2-0711-preview",
                    "messages": [{"role": "user", "content": "测试"}],
                    "max_tokens": 50
                }
            # Anthropic格式
            else:
                headers["anthropic-version"] = "2023-06-01"
                data = {
                    "model": "kimi-k2-0711-preview",
                    "max_tokens": 50,
                    "messages": [{"role": "user", "content": "测试"}]
                }
            
            response = requests.post(endpoint, headers=headers, json=data, timeout=15)
            print(f"     状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"     ✅ 成功! 响应: {response.json()}")
                return endpoint
            else:
                print(f"     响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"     ❌ 失败: {e}")
    
    return None

def test_openai_client():
    """使用OpenAI客户端测试"""
    print("\n=== 使用OpenAI客户端测试 ===")
    
    api_key = "sk-nGhmtCDstrz6ekvzpPo0S7jD9sLb2YIsr6LAhkKIa7gUG9tL"
    
    # 测试不同的base_url
    base_urls = [
        "https://api.moonshot.cn/v1",
        "https://api.moonshot.ai/v1",
        "https://api.moonshot.cn/anthropic",
        "https://api.moonshot.ai/anthropic"
    ]
    
    for base_url in base_urls:
        print(f"测试base_url: {base_url}")
        try:
            client = OpenAI(
                api_key=api_key,
                base_url=base_url,
                timeout=30
            )
            
            response = client.chat.completions.create(
                model="kimi-k2-0711-preview",
                messages=[{"role": "user", "content": "请回复'测试成功'"}],
                max_tokens=50
            )
            
            print(f"   ✅ 成功! 响应: {response.choices[0].message.content}")
            return base_url
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
    
    return None

def test_with_different_models():
    """测试不同的模型名称"""
    print("\n=== 测试不同的模型名称 ===")
    
    api_key = "sk-nGhmtCDstrz6ekvzpPo0S7jD9sLb2YIsr6LAhkKIa7gUG9tL"
    base_url = "https://api.moonshot.cn/v1"
    
    models = [
        "kimi-k2-0711-preview",
        "kimi-k2",
        "moonshot-v1-8k",
        "moonshot-v1-32k",
        "moonshot-v1-128k"
    ]
    
    for model in models:
        print(f"测试模型: {model}")
        try:
            client = OpenAI(
                api_key=api_key,
                base_url=base_url,
                timeout=30
            )
            
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": "测试"}],
                max_tokens=20
            )
            
            print(f"   ✅ 成功! 模型 {model} 可用")
            return model
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
    
    return None

if __name__ == "__main__":
    print("Kimi API调试工具")
    print("=" * 50)
    
    # 运行所有测试
    working_endpoint = test_kimi_api_direct()
    working_base_url = test_openai_client()
    working_model = test_with_different_models()
    
    print("\n" + "=" * 50)
    print("调试结果总结:")
    if working_endpoint:
        print(f"✅ 可用的API端点: {working_endpoint}")
    if working_base_url:
        print(f"✅ 可用的base_url: {working_base_url}")
    if working_model:
        print(f"✅ 可用的模型: {working_model}")
    
    if not any([working_endpoint, working_base_url, working_model]):
        print("❌ 所有测试都失败了，可能的原因:")
        print("1. API密钥无效或已过期")
        print("2. 网络连接问题")
        print("3. API服务暂时不可用")
        print("4. 需要使用不同的API端点或格式")
