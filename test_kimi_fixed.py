#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的Kimi集成
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_adapters import create_llm_adapter

def test_kimi_fixed():
    """测试修复后的Kimi适配器"""
    print("=== 测试修复后的Kimi适配器 ===")
    
    api_key = "sk-nGhmtCDstrz6ekvzpPo0S7jD9sLb2YIsr6LAhkKIa7gUG9tL"
    base_url = "https://api.moonshot.cn/v1"  # 修复后的URL
    model_name = "kimi-k2-0711-preview"
    
    try:
        adapter = create_llm_adapter(
            interface_format="kimi",
            base_url=base_url,
            model_name=model_name,
            api_key=api_key,
            temperature=0.7,
            max_tokens=100,
            timeout=30
        )
        
        print(f"✅ Kimi适配器创建成功")
        print(f"   - 基础URL: {base_url}")
        print(f"   - 模型名称: {model_name}")
        
        # 测试实际调用
        print("   - 正在测试API调用...")
        response = adapter.invoke("请回复'Kimi K2集成成功'")
        print(f"   - ✅ API调用成功!")
        print(f"   - 响应: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("Kimi K2修复测试")
    print("=" * 50)
    
    success = test_kimi_fixed()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Kimi K2集成修复成功！")
        print("\n现在您可以在应用程序中:")
        print("1. 选择接口格式为 'Kimi'")
        print("2. 使用您的API密钥")
        print("3. 确认base_url为: https://api.moonshot.cn/v1")
        print("4. 开始使用Kimi K2生成小说!")
    else:
        print("❌ 仍有问题需要解决")
