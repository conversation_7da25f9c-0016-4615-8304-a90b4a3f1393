#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Kimi K2集成的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_adapters import create_llm_adapter
from embedding_adapters import create_embedding_adapter

def test_kimi_llm():
    """测试Kimi LLM适配器"""
    print("=== 测试Kimi LLM适配器 ===")
    
    # 注意：这里需要真实的API密钥才能测试
    api_key = "your-kimi-api-key-here"  # 请替换为真实的API密钥
    base_url = "https://api.moonshot.cn/anthropic"
    model_name = "kimi-k2-0711-preview"
    
    try:
        adapter = create_llm_adapter(
            interface_format="kimi",
            base_url=base_url,
            model_name=model_name,
            api_key=api_key,
            temperature=0.7,
            max_tokens=100,
            timeout=30
        )
        
        print(f"✅ Kimi LLM适配器创建成功")
        print(f"   - 接口格式: kimi")
        print(f"   - 基础URL: {base_url}")
        print(f"   - 模型名称: {model_name}")
        
        # 如果有真实API密钥，可以测试实际调用
        if api_key != "your-kimi-api-key-here":
            response = adapter.invoke("请回复'测试成功'")
            print(f"   - 测试响应: {response}")
        else:
            print("   - 跳过实际API调用（需要真实API密钥）")
            
    except Exception as e:
        print(f"❌ Kimi LLM适配器测试失败: {e}")

def test_kimi_embedding():
    """测试Kimi Embedding适配器"""
    print("\n=== 测试Kimi Embedding适配器 ===")
    
    # 注意：这里需要真实的API密钥才能测试
    api_key = "your-kimi-api-key-here"  # 请替换为真实的API密钥
    base_url = "https://api.moonshot.cn/anthropic"
    model_name = "text-embedding-v1"
    
    try:
        adapter = create_embedding_adapter(
            interface_format="kimi",
            api_key=api_key,
            base_url=base_url,
            model_name=model_name
        )
        
        print(f"✅ Kimi Embedding适配器创建成功")
        print(f"   - 接口格式: kimi")
        print(f"   - 基础URL: {base_url}")
        print(f"   - 模型名称: {model_name}")
        
        # 如果有真实API密钥，可以测试实际调用
        if api_key != "your-kimi-api-key-here":
            embeddings = adapter.embed_query("测试文本")
            print(f"   - 向量维度: {len(embeddings) if embeddings else 0}")
        else:
            print("   - 跳过实际API调用（需要真实API密钥）")
            
    except Exception as e:
        print(f"❌ Kimi Embedding适配器测试失败: {e}")

def test_factory_functions():
    """测试工厂函数是否正确识别Kimi"""
    print("\n=== 测试工厂函数 ===")
    
    # 测试LLM工厂函数
    try:
        adapter = create_llm_adapter(
            interface_format="kimi",
            base_url="https://api.moonshot.cn/anthropic",
            model_name="kimi-k2-0711-preview",
            api_key="test-key",
            temperature=0.7,
            max_tokens=100,
            timeout=30
        )
        print(f"✅ LLM工厂函数正确识别Kimi: {type(adapter).__name__}")
    except Exception as e:
        print(f"❌ LLM工厂函数测试失败: {e}")
    
    # 测试Embedding工厂函数
    try:
        adapter = create_embedding_adapter(
            interface_format="kimi",
            api_key="test-key",
            base_url="https://api.moonshot.cn/anthropic",
            model_name="text-embedding-v1"
        )
        print(f"✅ Embedding工厂函数正确识别Kimi: {type(adapter).__name__}")
    except Exception as e:
        print(f"❌ Embedding工厂函数测试失败: {e}")

if __name__ == "__main__":
    print("Kimi K2集成测试")
    print("=" * 50)
    
    test_factory_functions()
    test_kimi_llm()
    test_kimi_embedding()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n注意事项：")
    print("1. 要进行完整测试，请在脚本中设置真实的Kimi API密钥")
    print("2. 确保网络连接正常，能够访问 https://api.moonshot.cn")
    print("3. 检查API密钥是否有足够的配额")
